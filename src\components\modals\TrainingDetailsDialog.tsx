import { useEffect, useState } from "react";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ListChecks } from "lucide-react";
import type { Training, TrainingExercise } from "@/api/api";
import { getTrainingExercises } from "@/api";

interface TrainingDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  training: Training | null;
}

export function TrainingDetailsDialog({ open, onOpenChange, training }: TrainingDetailsDialogProps) {
  const [exercises, setExercises] = useState<TrainingExercise[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!open || !training) return;
    setLoading(true);
    setError(null);
    getTrainingExercises(training.id)
      .then(setExercises)
      .catch((e) => setError(e.message))
      .finally(() => setLoading(false));
  }, [open, training]);

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-lg">
        <DialogHeader>
          <DialogTitle>Detalhes do Treino</DialogTitle>
        </DialogHeader>
        {training && (
          <div className="mb-4">
            <div className="font-medium text-lg">{training.name}</div>
            <div className="text-sm text-muted-foreground mb-2">
              {training.type} | {training.date}
            </div>
            <div className="text-sm text-muted-foreground mb-2">
              Horário: {training.start_time}{training.end_time ? ` - ${training.end_time}` : ''} | Local: {training.location}
            </div>
            <div className="text-xs text-muted-foreground mb-2">Treinador: {training.coach}</div>
            <div className="text-xs text-muted-foreground">Status: {training.status}</div>
          </div>
        )}
        <div>
          <div className="font-medium mb-2">Exercícios deste treino</div>
          {loading ? (
            <div className="text-center text-muted-foreground">Carregando exercícios...</div>
          ) : error ? (
            <div className="text-center text-red-500">{error}</div>
          ) : exercises.length === 0 ? (
            <div className="text-center text-muted-foreground">Nenhum exercício associado a este treino.</div>
          ) : (
            <ul className="divide-y rounded border bg-muted">
              {exercises
                .sort((a, b) => a.order_in_training - b.order_in_training)
                .map((ex, idx) => (
                  <li key={ex.id} className="p-3">
                    <div className="flex items-center gap-2">
                      <span className="font-bold text-sm">{idx + 1}.</span>
                      <span className="font-medium">{ex.exercises.name}</span>
                      <span className="text-xs text-muted-foreground">({ex.exercises.category} - {ex.exercises.difficulty})</span>
                    </div>
                    {ex.notes && (
                      <div className="text-xs text-muted-foreground mt-1">Notas: {ex.notes}</div>
                    )}
                  </li>
                ))}
            </ul>
          )}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Fechar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
