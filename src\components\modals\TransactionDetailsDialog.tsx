import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { FinancialTransaction } from "@/api/api";
import { FileMinus, FilePlus } from "lucide-react";

interface TransactionDetailsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  transaction: FinancialTransaction | null;
}

export function TransactionDetailsDialog({ open, onOpenChange, transaction }: TransactionDetailsDialogProps) {
  if (!transaction) return null;

  // Format date
  const formattedDate = transaction.date 
    ? new Date(transaction.date).toLocaleDateString('pt-BR')
    : '-';

  // Format amount
  const formattedAmount = typeof transaction.amount === 'string'
    ? `R$ ${parseFloat(transaction.amount).toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`
    : `R$ ${transaction.amount.toLocaleString('pt-BR', { minimumFractionDigits: 2 })}`;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Detalhes da Transação</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div className="flex items-center gap-2 mb-4">
            {transaction.type === "receita" ? (
              <FilePlus className="h-5 w-5 text-green-600" />
            ) : (
              <FileMinus className="h-5 w-5 text-red-600" />
            )}
            <h3 className="text-lg font-semibold">{transaction.description}</h3>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm text-muted-foreground">Tipo</p>
              <Badge
                variant="outline"
                className={
                  transaction.type === "receita"
                    ? "bg-green-50 text-green-700 border-green-200"
                    : "bg-red-50 text-red-700 border-red-200"
                }
              >
                {transaction.type === "receita" ? "Receita" : "Despesa"}
              </Badge>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Categoria</p>
              <p className="font-medium">{transaction.category}</p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Valor</p>
              <p className={`font-medium ${transaction.type === "receita" ? "text-green-600" : "text-red-600"}`}>
                {formattedAmount}
              </p>
            </div>
            <div>
              <p className="text-sm text-muted-foreground">Data</p>
              <p className="font-medium">{formattedDate}</p>
            </div>
          </div>

          {transaction.player_name && (
            <div className="mt-4">
              <p className="text-sm text-muted-foreground">Jogador Associado</p>
              <p className="font-medium">{transaction.player_name}</p>
            </div>
          )}

          <div className="mt-4">
            <p className="text-sm text-muted-foreground">ID da Transação</p>
            <p className="font-medium text-xs">{transaction.id}</p>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Fechar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
