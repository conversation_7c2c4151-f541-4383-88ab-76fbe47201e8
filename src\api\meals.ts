import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";

// Tipos
export type MealType = Database["public"]["Tables"]["meal_types"]["Row"];
export type MealSession = Database["public"]["Tables"]["meal_sessions"]["Row"];
export type MealParticipant = Database["public"]["Tables"]["meal_participants"]["Row"];

// Tipos estendidos para incluir dados relacionados
export type MealSessionWithDetails = MealSession & {
  meal_type_name?: string;
  accommodation_name?: string;
  participant_count?: number;
  meal_types?: {
    name: string;
    location?: string;
    address?: string;
  };
  accommodations?: {
    name: string;
  };
};

export type MealParticipantWithDetails = MealParticipant & {
  participant_name?: string;
  participant_nickname?: string;
  participant_role?: string;
  players?: {
    name: string;
    nickname?: string;
  };
  collaborators?: {
    full_name: string;
    role?: string;
  };
};

// ===== MEAL TYPES =====

export async function getMealTypes(clubId: number): Promise<MealType[]> {
  const { data, error } = await supabase
    .from("meal_types")
    .select("*")
    .eq("club_id", clubId)
    .order("name");

  if (error) {
    console.error("Erro ao buscar tipos de refeição:", error);
    throw new Error(`Erro ao buscar tipos de refeição: ${error.message}`);
  }

  return data || [];
}

export async function createMealType(
  clubId: number,
  mealType: Omit<MealType, "id" | "club_id" | "created_at">
): Promise<MealType> {
  const { data, error } = await supabase
    .from("meal_types")
    .insert({
      ...mealType,
      club_id: clubId
    })
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar tipo de refeição:", error);
    throw new Error(`Erro ao criar tipo de refeição: ${error.message}`);
  }

  return data;
}

export async function updateMealType(
  clubId: number,
  id: number,
  mealType: Partial<MealType>
): Promise<MealType> {
  const { data, error } = await supabase
    .from("meal_types")
    .update(mealType)
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error(`Erro ao atualizar tipo de refeição ${id}:`, error);
    throw new Error(`Erro ao atualizar tipo de refeição: ${error.message}`);
  }

  return data;
}

export async function deleteMealType(clubId: number, id: number): Promise<boolean> {
  // Verificar se há sessões de refeição usando este tipo
  const { data: sessions, error: sessionsError } = await supabase
    .from("meal_sessions")
    .select("id")
    .eq("club_id", clubId)
    .eq("meal_type_id", id);

  if (sessionsError) {
    console.error("Erro ao verificar sessões de refeição:", sessionsError);
    throw new Error(`Erro ao verificar sessões de refeição: ${sessionsError.message}`);
  }

  if (sessions && sessions.length > 0) {
    throw new Error("Não é possível excluir este tipo de refeição pois há sessões associadas a ele.");
  }

  const { error } = await supabase
    .from("meal_types")
    .delete()
    .eq("club_id", clubId)
    .eq("id", id);

  if (error) {
    console.error(`Erro ao excluir tipo de refeição ${id}:`, error);
    throw new Error(`Erro ao excluir tipo de refeição: ${error.message}`);
  }

  return true;
}

// ===== MEAL SESSIONS =====

export async function getMealSessions(clubId: number, accommodationId?: number): Promise<MealSessionWithDetails[]> {
  let query = supabase
    .from("meal_sessions")
    .select(`
      *,
      meal_types:meal_type_id (name, location, address),
      accommodations:accommodation_id (name)
    `)
    .eq("club_id", clubId)
    .order("date", { ascending: false })
    .order("time", { ascending: false });

  if (accommodationId) {
    query = query.eq("accommodation_id", accommodationId);
  }

  const { data, error } = await query;

  if (error) {
    console.error("Erro ao buscar sessões de refeição:", error);
    throw new Error(`Erro ao buscar sessões de refeição: ${error.message}`);
  }

  // Buscar contagem de participantes para cada sessão
  const sessionsWithCount = await Promise.all(
    (data || []).map(async (session) => {
      const { data: participants, error: participantsError } = await supabase
        .from("meal_participants")
        .select("id")
        .eq("meal_session_id", session.id);

      if (participantsError) {
        console.error("Erro ao contar participantes:", participantsError);
      }

      return {
        ...session,
        meal_type_name: session.meal_types?.name,
        accommodation_name: session.accommodations?.name,
        participant_count: participants?.length || 0
      };
    })
  );

  return sessionsWithCount;
}

export async function createMealSession(
  clubId: number,
  mealSession: Omit<MealSession, "id" | "club_id" | "created_at">
): Promise<MealSession> {
  const { data, error } = await supabase
    .from("meal_sessions")
    .insert({
      ...mealSession,
      club_id: clubId
    })
    .select()
    .single();

  if (error) {
    console.error("Erro ao criar sessão de refeição:", error);
    throw new Error(`Erro ao criar sessão de refeição: ${error.message}`);
  }

  return data;
}

export async function updateMealSession(
  clubId: number,
  id: number,
  mealSession: Partial<MealSession>
): Promise<MealSession> {
  const { data, error } = await supabase
    .from("meal_sessions")
    .update(mealSession)
    .eq("club_id", clubId)
    .eq("id", id)
    .select()
    .single();

  if (error) {
    console.error(`Erro ao atualizar sessão de refeição ${id}:`, error);
    throw new Error(`Erro ao atualizar sessão de refeição: ${error.message}`);
  }

  return data;
}

export async function deleteMealSession(clubId: number, id: number): Promise<boolean> {
  // Primeiro, excluir todos os participantes da sessão
  const { error: participantsError } = await supabase
    .from("meal_participants")
    .delete()
    .eq("club_id", clubId)
    .eq("meal_session_id", id);

  if (participantsError) {
    console.error("Erro ao excluir participantes da sessão:", participantsError);
    throw new Error(`Erro ao excluir participantes da sessão: ${participantsError.message}`);
  }

  // Depois, excluir a sessão
  const { error } = await supabase
    .from("meal_sessions")
    .delete()
    .eq("club_id", clubId)
    .eq("id", id);

  if (error) {
    console.error(`Erro ao excluir sessão de refeição ${id}:`, error);
    throw new Error(`Erro ao excluir sessão de refeição: ${error.message}`);
  }

  return true;
}

// ===== MEAL PARTICIPANTS =====

export async function getMealParticipants(clubId: number, mealSessionId: number): Promise<MealParticipantWithDetails[]> {
  const { data, error } = await supabase
    .from("meal_participants")
    .select(`
      *,
      players:participant_id (name, nickname),
      collaborators:participant_id (full_name, role)
    `)
    .eq("club_id", clubId)
    .eq("meal_session_id", mealSessionId)
    .order("participant_type")
    .order("created_at");

  if (error) {
    console.error("Erro ao buscar participantes da refeição:", error);
    throw new Error(`Erro ao buscar participantes da refeição: ${error.message}`);
  }

  // Formatar os dados para incluir nomes dos participantes
  return (data || []).map(participant => ({
    ...participant,
    participant_name: participant.participant_type === 'player' 
      ? participant.players?.name 
      : participant.collaborators?.full_name,
    participant_nickname: participant.participant_type === 'player' 
      ? participant.players?.nickname 
      : undefined,
    participant_role: participant.participant_type === 'collaborator' 
      ? participant.collaborators?.role 
      : undefined
  }));
}

export async function addMealParticipant(
  clubId: number,
  mealSessionId: number,
  participantId: string,
  participantType: 'player' | 'collaborator'
): Promise<MealParticipant> {
  // Verificar se o participante já está na sessão
  const { data: existing, error: existingError } = await supabase
    .from("meal_participants")
    .select("id")
    .eq("club_id", clubId)
    .eq("meal_session_id", mealSessionId)
    .eq("participant_id", participantId)
    .eq("participant_type", participantType);

  if (existingError) {
    console.error("Erro ao verificar participante existente:", existingError);
    throw new Error(`Erro ao verificar participante existente: ${existingError.message}`);
  }

  if (existing && existing.length > 0) {
    throw new Error("Este participante já está adicionado a esta refeição.");
  }

  const { data, error } = await supabase
    .from("meal_participants")
    .insert({
      club_id: clubId,
      meal_session_id: mealSessionId,
      participant_id: participantId,
      participant_type: participantType,
      signed: false
    })
    .select()
    .single();

  if (error) {
    console.error("Erro ao adicionar participante à refeição:", error);
    throw new Error(`Erro ao adicionar participante à refeição: ${error.message}`);
  }

  return data;
}

export async function removeMealParticipant(clubId: number, participantId: number): Promise<boolean> {
  const { error } = await supabase
    .from("meal_participants")
    .delete()
    .eq("club_id", clubId)
    .eq("id", participantId);

  if (error) {
    console.error(`Erro ao remover participante ${participantId}:`, error);
    throw new Error(`Erro ao remover participante: ${error.message}`);
  }

  return true;
}

export async function updateMealParticipantSignature(
  clubId: number,
  participantId: number,
  signed: boolean
): Promise<MealParticipant> {
  const { data, error } = await supabase
    .from("meal_participants")
    .update({ signed })
    .eq("club_id", clubId)
    .eq("id", participantId)
    .select()
    .single();

  if (error) {
    console.error(`Erro ao atualizar assinatura do participante ${participantId}:`, error);
    throw new Error(`Erro ao atualizar assinatura do participante: ${error.message}`);
  }

  return data;
}
