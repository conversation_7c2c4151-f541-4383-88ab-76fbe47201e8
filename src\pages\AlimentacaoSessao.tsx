import { useState, useEffect, useCallback } from "react";
import { useNavigate, useParams } from "react-router-dom";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { toast } from "@/components/ui/use-toast";
import { 
  ChevronLeft, 
  Users, 
  Clock, 
  MapPin, 
  FileText, 
  Download,
  UserPlus,
  Trash2
} from "lucide-react";
import { useCurrentClubId } from "@/context/ClubContext";
import { getAccommodationPlayers, getCollaboratorAccommodations } from "@/api/accommodations";
import { getPlayers, getCollaborators, getClubInfo, Player, Collaborator } from "@/api/api";
import {
  getMealSessions,
  getMealParticipants,
  addMealParticipant,
  removeMealParticipant,
  MealSessionWithDetails,
  MealParticipantWithDetails
} from "@/api/meals";
import { generateMealReport } from "@/utils/mealReportGenerator";

interface AccommodationUser {
  id: string;
  name: string;
  nickname?: string;
  role?: string;
  type: 'player' | 'collaborator';
  image?: string;
}

export default function AlimentacaoSessao() {
  const navigate = useNavigate();
  const { sessionId } = useParams<{ sessionId: string }>();
  const clubId = useCurrentClubId();

  // Estados principais
  const [mealSession, setMealSession] = useState<MealSessionWithDetails | null>(null);
  const [participants, setParticipants] = useState<MealParticipantWithDetails[]>([]);
  const [accommodationUsers, setAccommodationUsers] = useState<AccommodationUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [draggedUser, setDraggedUser] = useState<AccommodationUser | null>(null);

  // Função para carregar dados da sessão
  const fetchSessionData = useCallback(async () => {
    if (!sessionId) return;

    setLoading(true);
    try {
      // Buscar dados da sessão
      const sessions = await getMealSessions(clubId);
      const session = sessions.find(s => s.id === parseInt(sessionId));
      
      if (!session) {
        toast({
          title: "Erro",
          description: "Sessão de alimentação não encontrada.",
          variant: "destructive"
        });
        navigate("/alimentacao");
        return;
      }

      setMealSession(session);

      // Buscar participantes da sessão
      const participantsData = await getMealParticipants(clubId, parseInt(sessionId));
      setParticipants(participantsData);

      // Buscar usuários do alojamento
      if (session.accommodation_id) {
        const [playersInAccommodation, collaboratorsInAccommodation] = await Promise.all([
          getAccommodationPlayers(clubId, session.accommodation_id),
          getCollaboratorAccommodations(clubId)
        ]);

        // Filtrar colaboradores do alojamento específico
        const accommodationCollaborators = collaboratorsInAccommodation.filter(
          ca => ca.accommodation_id === session.accommodation_id
        );

        // Converter para formato unificado
        const users: AccommodationUser[] = [
          ...playersInAccommodation.map(pa => ({
            id: pa.player_id || '',
            name: pa.player_name || '',
            nickname: pa.player_nickname,
            type: 'player' as const,
            image: undefined // Pode ser adicionado se houver imagem do jogador
          })),
          ...accommodationCollaborators.map(ca => ({
            id: ca.collaborator_id?.toString() || '',
            name: ca.collaborator_name || '',
            role: ca.collaborator_role,
            type: 'collaborator' as const,
            image: undefined // Pode ser adicionado se houver imagem do colaborador
          }))
        ];

        setAccommodationUsers(users);
      }

      setLoading(false);
    } catch (error) {
      console.error("Erro ao carregar dados da sessão:", error);
      toast({
        title: "Erro",
        description: "Não foi possível carregar os dados da sessão.",
        variant: "destructive"
      });
      setLoading(false);
    }
  }, [clubId, sessionId, navigate]);

  // Carregar dados quando o componente montar
  useEffect(() => {
    if (clubId && sessionId) {
      fetchSessionData();
    }
  }, [clubId, sessionId, fetchSessionData]);

  // Handlers para drag and drop
  const handleDragStart = (user: AccommodationUser) => {
    setDraggedUser(user);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = async (e: React.DragEvent) => {
    e.preventDefault();
    
    if (!draggedUser || !sessionId) return;

    // Verificar se o usuário já está na lista de participantes
    const isAlreadyParticipant = participants.some(
      p => p.participant_id === draggedUser.id && p.participant_type === draggedUser.type
    );

    if (isAlreadyParticipant) {
      toast({
        title: "Aviso",
        description: "Este usuário já está participando desta refeição.",
        variant: "destructive"
      });
      setDraggedUser(null);
      return;
    }

    try {
      await addMealParticipant(clubId, parseInt(sessionId), draggedUser.id, draggedUser.type);
      toast({
        title: "Sucesso",
        description: `${draggedUser.name} foi adicionado à refeição.`,
      });
      
      // Recarregar participantes
      const participantsData = await getMealParticipants(clubId, parseInt(sessionId));
      setParticipants(participantsData);
    } catch (error: any) {
      toast({
        title: "Erro",
        description: error.message || "Erro ao adicionar participante.",
        variant: "destructive"
      });
    }

    setDraggedUser(null);
  };

  // Handler para remover participante
  const handleRemoveParticipant = async (participant: MealParticipantWithDetails) => {
    if (window.confirm(`Tem certeza que deseja remover ${participant.participant_name} desta refeição?`)) {
      try {
        await removeMealParticipant(clubId, participant.id);
        toast({
          title: "Sucesso",
          description: "Participante removido da refeição.",
        });
        
        // Recarregar participantes
        const participantsData = await getMealParticipants(clubId, parseInt(sessionId!));
        setParticipants(participantsData);
      } catch (error: any) {
        toast({
          title: "Erro",
          description: error.message || "Erro ao remover participante.",
          variant: "destructive"
        });
      }
    }
  };

  // Handler para gerar relatório
  const handleGenerateReport = async () => {
    if (!mealSession) return;

    try {
      const clubInfo = await getClubInfo(clubId);
      const blob = await generateMealReport(mealSession, participants, clubInfo);
      
      // Criar URL para download
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `relatorio-alimentacao-${mealSession.date}-${mealSession.meal_type_name?.replace(/\s+/g, '-').toLowerCase()}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      toast({
        title: "Sucesso",
        description: "Relatório gerado com sucesso.",
      });
    } catch (error) {
      console.error("Erro ao gerar relatório:", error);
      toast({
        title: "Erro",
        description: "Erro ao gerar relatório.",
        variant: "destructive"
      });
    }
  };

  // Filtrar usuários que não estão participando
  const availableUsers = accommodationUsers.filter(user => 
    !participants.some(p => p.participant_id === user.id && p.participant_type === user.type)
  );

  if (loading) {
    return (
      <div className="container mx-auto py-6">
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-team-blue"></div>
        </div>
      </div>
    );
  }

  if (!mealSession) {
    return (
      <div className="container mx-auto py-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Sessão não encontrada</h2>
          <Button onClick={() => navigate("/alimentacao")}>
            Voltar para Alimentação
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button variant="ghost" onClick={() => navigate("/alimentacao")} className="mr-2">
            <ChevronLeft className="h-4 w-4 mr-1" />
            Voltar
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Sessão de Alimentação</h1>
            <p className="text-gray-600">{mealSession.meal_type_name} - {mealSession.accommodation_name}</p>
          </div>
        </div>
        <Button onClick={handleGenerateReport}>
          <Download className="h-4 w-4 mr-1" />
          Gerar Relatório
        </Button>
      </div>

      {/* Informações da sessão */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Detalhes da Sessão</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center">
              <Clock className="h-4 w-4 mr-2 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500">Data e Horário</p>
                <p className="font-medium">
                  {new Date(mealSession.date).toLocaleDateString('pt-BR')}
                  {mealSession.time && ` às ${mealSession.time}`}
                </p>
              </div>
            </div>
            
            <div className="flex items-center">
              <MapPin className="h-4 w-4 mr-2 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500">Local</p>
                <p className="font-medium">{mealSession.meal_types?.location || "Não informado"}</p>
              </div>
            </div>
            
            <div className="flex items-center">
              <Users className="h-4 w-4 mr-2 text-gray-500" />
              <div>
                <p className="text-sm text-gray-500">Participantes</p>
                <p className="font-medium">{participants.length}</p>
              </div>
            </div>
          </div>
          
          {mealSession.notes && (
            <div className="mt-4">
              <p className="text-sm text-gray-500">Observações</p>
              <p className="text-gray-700">{mealSession.notes}</p>
            </div>
          )}
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Usuários disponíveis */}
        <Card>
          <CardHeader>
            <CardTitle>Usuários do Alojamento</CardTitle>
            <p className="text-sm text-gray-600">
              Arraste os usuários para a área de participantes
            </p>
          </CardHeader>
          <CardContent>
            {availableUsers.length === 0 ? (
              <p className="text-center text-gray-500 py-4">
                Todos os usuários do alojamento já estão participando
              </p>
            ) : (
              <div className="space-y-2">
                {availableUsers.map((user) => (
                  <div
                    key={`${user.type}-${user.id}`}
                    draggable
                    onDragStart={() => handleDragStart(user)}
                    className="flex items-center p-3 border rounded-lg cursor-move hover:bg-gray-50 transition-colors"
                  >
                    <Avatar className="h-8 w-8 mr-3">
                      <AvatarImage src={user.image} />
                      <AvatarFallback>
                        {user.name.charAt(0).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div className="flex-1">
                      <p className="font-medium">{user.name}</p>
                      {user.nickname && (
                        <p className="text-sm text-gray-500">"{user.nickname}"</p>
                      )}
                      {user.role && (
                        <p className="text-sm text-gray-500">{user.role}</p>
                      )}
                    </div>
                    <Badge variant={user.type === 'player' ? 'default' : 'secondary'}>
                      {user.type === 'player' ? 'Jogador' : 'Colaborador'}
                    </Badge>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Participantes da refeição */}
        <Card>
          <CardHeader>
            <CardTitle>Participantes da Refeição</CardTitle>
            <p className="text-sm text-gray-600">
              Solte os usuários aqui para adicionar à refeição
            </p>
          </CardHeader>
          <CardContent
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            className="min-h-[300px] border-2 border-dashed border-gray-200 rounded-lg p-4"
          >
            {participants.length === 0 ? (
              <div className="flex items-center justify-center h-full text-gray-500">
                <div className="text-center">
                  <UserPlus className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>Nenhum participante adicionado</p>
                  <p className="text-sm">Arraste usuários para cá</p>
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                {participants.map((participant) => (
                  <div
                    key={participant.id}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div className="flex items-center">
                      <Avatar className="h-8 w-8 mr-3">
                        <AvatarFallback>
                          {participant.participant_name?.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{participant.participant_name}</p>
                        {participant.participant_nickname && (
                          <p className="text-sm text-gray-500">"{participant.participant_nickname}"</p>
                        )}
                        {participant.participant_role && (
                          <p className="text-sm text-gray-500">{participant.participant_role}</p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant={participant.participant_type === 'player' ? 'default' : 'secondary'}>
                        {participant.participant_type === 'player' ? 'Jogador' : 'Colaborador'}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleRemoveParticipant(participant)}
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
